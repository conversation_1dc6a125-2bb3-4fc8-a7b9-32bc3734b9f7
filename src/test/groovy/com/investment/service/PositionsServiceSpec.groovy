package com.investment.service

import com.investment.api.model.CreatePositionRequest
import com.investment.api.model.UpdatePositionRequest
import com.investment.database.DatabaseManager
import com.investment.model.Position
import spock.lang.Specification

import java.math.BigDecimal
import java.sql.Connection
import java.sql.PreparedStatement
import java.sql.ResultSet
import java.sql.SQLException
import java.sql.Statement
import java.sql.Timestamp
import java.time.LocalDateTime

/**
 * Test specification for PositionsService.
 */
class PositionsServiceSpec extends Specification {

    PositionsService positionsService
    DatabaseManager databaseManager

    def setup() {
        databaseManager = Mock(DatabaseManager)
        positionsService = new PositionsService(databaseManager)
    }

    def "should create position successfully"() {
        given: "a valid create position request"
        def request = new CreatePositionRequest("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.25"))
        request.setInitPortfolioNetValue(new BigDecimal("100000"))
        request.setRiskUnit(new BigDecimal("1000"))
        request.setStopPercent(new BigDecimal("0.02"))

        and: "symbol exists in instruments table"
        databaseManager.symbolExists("AAPL") >> true

        and: "database creates position successfully"
        databaseManager.createPosition(
                "AAPL",
                new BigDecimal("100"),
                "BUY",
                "OPEN",
                new BigDecimal("150.25"),
                new BigDecimal("15025.00"),
                new BigDecimal("100000"),
                new BigDecimal("1000"),
                new BigDecimal("0.02"),
                null
        ) >> 1L

        when: "creating the position"
        def position = positionsService.createPosition(request)

        then: "position should be created with correct data"
        position.id == 1L
        position.symbol == "AAPL"
        position.position == new BigDecimal("100")
        position.side == Position.Side.BUY
        position.tradePrice == new BigDecimal("150.25")
        position.tradeValue == new BigDecimal("15025.00")
        position.status == Position.Status.OPEN
        position.initPortfolioNetValue == new BigDecimal("100000")
        position.riskUnit == new BigDecimal("1000")
        position.stopPercent == new BigDecimal("0.02")
    }

    def "should throw exception when symbol does not exist"() {
        given: "a create position request with non-existent symbol"
        def request = new CreatePositionRequest("INVALID", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.25"))

        and: "symbol does not exist in instruments table"
        databaseManager.symbolExists("INVALID") >> false

        when: "creating the position"
        positionsService.createPosition(request)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Symbol not found in instruments: INVALID")
    }

    def "should get position by ID successfully"() {
        given: "a position ID"
        def positionId = 1L

        and: "database returns position data"
        def positionData = [
                id: 1L,
                symbol: "AAPL",
                position: new BigDecimal("100"),
                side: "BUY",
                status: "OPEN",
                trade_price: new BigDecimal("150.25"),
                trade_value: new BigDecimal("15025.00"),
                init_portfolio_net_value: new BigDecimal("100000"),
                last_price: new BigDecimal("155.00"),
                last_value: new BigDecimal("15500.00"),
                risk_unit: new BigDecimal("1000"),
                stop_percent: new BigDecimal("0.02"),
                highest_after_trade: new BigDecimal("155.00"),
                stop_value_from_highest: new BigDecimal("151.90"),
                last_bbmb: new BigDecimal("152.00"),
                bbmb_adj_percent: new BigDecimal("0.01"),
                stop_value_from_bbmb: new BigDecimal("150.48"),
                expand_or_contract: "EXPANDING",
                effective_stop_value: new BigDecimal("151.90"),
                pnl_value: new BigDecimal("475.00"),
                pnl_percent: new BigDecimal("0.0316"),
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]

        databaseManager.getPositionById(positionId) >> positionData

        when: "getting position by ID"
        def optionalPosition = positionsService.getPositionById(positionId)

        then: "should return position with correct data"
        optionalPosition.isPresent()
        def position = optionalPosition.get()
        position.id == 1L
        position.symbol == "AAPL"
        position.position == new BigDecimal("100")
        position.side == Position.Side.BUY
        position.status == Position.Status.OPEN
        position.expandOrContract == Position.ExpandOrContract.EXPANDING
    }

    def "should return empty optional when position not found"() {
        given: "a position ID that doesn't exist"
        def positionId = 999L

        and: "database returns no results"
        connection.prepareStatement(_) >> preparedStatement
        preparedStatement.executeQuery() >> resultSet
        resultSet.next() >> false

        when: "getting position by ID"
        def optionalPosition = positionsService.getPositionById(positionId)

        then: "should return empty optional"
        !optionalPosition.isPresent()
    }

    def "should get positions with filters"() {
        given: "filter parameters"
        def symbol = "AAPL"
        def status = Position.Status.OPEN
        def side = Position.Side.BUY

        and: "database returns filtered results"
        connection.prepareStatement(_) >> preparedStatement
        preparedStatement.executeQuery() >> resultSet
        resultSet.next() >>> [true, true, false] // Two results

        // Mock ResultSet for first position
        resultSet.getLong("id") >>> [1L, 2L]
        resultSet.getString("symbol") >>> ["AAPL", "AAPL"]
        resultSet.getBigDecimal("position") >>> [new BigDecimal("100"), new BigDecimal("200")]
        resultSet.getString("side") >>> ["BUY", "BUY"]
        resultSet.getString("status") >>> ["OPEN", "OPEN"]
        resultSet.getBigDecimal("trade_price") >>> [new BigDecimal("150.25"), new BigDecimal("148.50")]
        resultSet.getBigDecimal("trade_value") >>> [new BigDecimal("15025.00"), new BigDecimal("29700.00")]
        
        // Mock other fields as null for simplicity
        resultSet.getBigDecimal("init_portfolio_net_value") >>> [null, null]
        resultSet.getBigDecimal("last_price") >>> [null, null]
        resultSet.getBigDecimal("last_value") >>> [null, null]
        resultSet.getBigDecimal("risk_unit") >>> [null, null]
        resultSet.getBigDecimal("stop_percent") >>> [null, null]
        resultSet.getBigDecimal("highest_after_trade") >>> [null, null]
        resultSet.getBigDecimal("stop_value_from_highest") >>> [null, null]
        resultSet.getBigDecimal("last_bbmb") >>> [null, null]
        resultSet.getBigDecimal("bbmb_adj_percent") >>> [null, null]
        resultSet.getBigDecimal("stop_value_from_bbmb") >>> [null, null]
        resultSet.getString("expand_or_contract") >>> [null, null]
        resultSet.getBigDecimal("effective_stop_value") >>> [null, null]
        resultSet.getBigDecimal("pnl_value") >>> [null, null]
        resultSet.getBigDecimal("pnl_percent") >>> [null, null]
        resultSet.getTimestamp("created_date") >>> [Timestamp.valueOf(LocalDateTime.now()), Timestamp.valueOf(LocalDateTime.now())]
        resultSet.getTimestamp("updated_date") >>> [Timestamp.valueOf(LocalDateTime.now()), Timestamp.valueOf(LocalDateTime.now())]

        when: "getting positions with filters"
        def positions = positionsService.getPositions(symbol, status, side)

        then: "should return filtered positions"
        positions.size() == 2
        positions[0].symbol == "AAPL"
        positions[0].side == Position.Side.BUY
        positions[0].status == Position.Status.OPEN
        positions[1].symbol == "AAPL"
        positions[1].side == Position.Side.BUY
        positions[1].status == Position.Status.OPEN

        and: "database should be queried with filters"
        1 * preparedStatement.setObject(1, "AAPL")
        1 * preparedStatement.setObject(2, "OPEN")
        1 * preparedStatement.setObject(3, "BUY")
    }

    def "should update position successfully"() {
        given: "an existing position"
        def positionId = 1L
        def updateRequest = new UpdatePositionRequest()
        updateRequest.setLastPrice(new BigDecimal("155.00"))
        updateRequest.setRiskUnit(new BigDecimal("1200"))
        updateRequest.setStatus(Position.Status.CLOSED)

        and: "position exists in database"
        setupMockPositionRetrieval(positionId)

        and: "update statement setup"
        connection.prepareStatement(_) >> preparedStatement

        when: "updating the position"
        def updatedPosition = positionsService.updatePosition(positionId, updateRequest)

        then: "position should be updated"
        updatedPosition.id == positionId
        updatedPosition.lastPrice == new BigDecimal("155.00")
        updatedPosition.riskUnit == new BigDecimal("1200")
        updatedPosition.status == Position.Status.CLOSED

        and: "database should be updated"
        1 * preparedStatement.executeUpdate()
    }

    def "should throw exception when updating non-existent position"() {
        given: "a non-existent position ID"
        def positionId = 999L
        def updateRequest = new UpdatePositionRequest()
        updateRequest.setLastPrice(new BigDecimal("155.00"))

        and: "position does not exist"
        connection.prepareStatement(_) >> preparedStatement
        preparedStatement.executeQuery() >> resultSet
        resultSet.next() >> false

        when: "updating the position"
        positionsService.updatePosition(positionId, updateRequest)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Position not found: 999")
    }

    def "should close position successfully"() {
        given: "an existing open position"
        def positionId = 1L

        and: "position exists in database"
        setupMockPositionRetrieval(positionId)

        and: "update statement setup"
        connection.prepareStatement(_) >> preparedStatement

        when: "closing the position"
        def closedPosition = positionsService.closePosition(positionId)

        then: "position should be closed"
        closedPosition.status == Position.Status.CLOSED
        closedPosition.updatedDate != null

        and: "database should be updated"
        1 * preparedStatement.executeUpdate()
    }

    def "should delete position successfully"() {
        given: "a position ID"
        def positionId = 1L

        and: "delete statement setup"
        connection.prepareStatement("DELETE FROM positions WHERE id = ?") >> preparedStatement
        preparedStatement.executeUpdate() >> 1

        when: "deleting the position"
        def deleted = positionsService.deletePosition(positionId)

        then: "should return true"
        deleted

        and: "database should be updated"
        1 * preparedStatement.setLong(1, positionId)
        1 * preparedStatement.executeUpdate()
    }

    def "should return false when deleting non-existent position"() {
        given: "a non-existent position ID"
        def positionId = 999L

        and: "delete statement returns 0 affected rows"
        connection.prepareStatement("DELETE FROM positions WHERE id = ?") >> preparedStatement
        preparedStatement.executeUpdate() >> 0

        when: "deleting the position"
        def deleted = positionsService.deletePosition(positionId)

        then: "should return false"
        !deleted
    }

    def "should get open positions"() {
        given: "database returns open positions"
        connection.prepareStatement(_) >> preparedStatement
        preparedStatement.executeQuery() >> resultSet
        resultSet.next() >>> [true, false] // One result

        // Mock minimal ResultSet data
        setupMinimalResultSetMocking()

        when: "getting open positions"
        def positions = positionsService.getOpenPositions()

        then: "should return open positions"
        positions.size() == 1
        positions[0].status == Position.Status.OPEN
    }

    def "should handle SQL exceptions gracefully"() {
        given: "database throws SQLException"
        connection.prepareStatement(_) >> { throw new SQLException("Database error") }

        when: "performing database operation"
        positionsService.getPositionById(1L)

        then: "should propagate SQLException"
        thrown(SQLException)
    }

    private void setupMockPositionRetrieval(Long positionId) {
        connection.prepareStatement(_) >>> [preparedStatement, preparedStatement]
        preparedStatement.executeQuery() >> resultSet
        resultSet.next() >> true
        
        // Mock basic position data
        resultSet.getLong("id") >> positionId
        resultSet.getString("symbol") >> "AAPL"
        resultSet.getBigDecimal("position") >> new BigDecimal("100")
        resultSet.getString("side") >> "BUY"
        resultSet.getString("status") >> "OPEN"
        resultSet.getBigDecimal("trade_price") >> new BigDecimal("150.25")
        resultSet.getBigDecimal("trade_value") >> new BigDecimal("15025.00")
        
        // Mock other fields as null
        resultSet.getBigDecimal("init_portfolio_net_value") >> null
        resultSet.getBigDecimal("last_price") >> null
        resultSet.getBigDecimal("last_value") >> null
        resultSet.getBigDecimal("risk_unit") >> null
        resultSet.getBigDecimal("stop_percent") >> null
        resultSet.getBigDecimal("highest_after_trade") >> null
        resultSet.getBigDecimal("stop_value_from_highest") >> null
        resultSet.getBigDecimal("last_bbmb") >> null
        resultSet.getBigDecimal("bbmb_adj_percent") >> null
        resultSet.getBigDecimal("stop_value_from_bbmb") >> null
        resultSet.getString("expand_or_contract") >> null
        resultSet.getBigDecimal("effective_stop_value") >> null
        resultSet.getBigDecimal("pnl_value") >> null
        resultSet.getBigDecimal("pnl_percent") >> null
        resultSet.getTimestamp("created_date") >> Timestamp.valueOf(LocalDateTime.now())
        resultSet.getTimestamp("updated_date") >> Timestamp.valueOf(LocalDateTime.now())
    }

    private void setupMinimalResultSetMocking() {
        resultSet.getLong("id") >> 1L
        resultSet.getString("symbol") >> "AAPL"
        resultSet.getBigDecimal("position") >> new BigDecimal("100")
        resultSet.getString("side") >> "BUY"
        resultSet.getString("status") >> "OPEN"
        resultSet.getBigDecimal("trade_price") >> new BigDecimal("150.25")
        resultSet.getBigDecimal("trade_value") >> new BigDecimal("15025.00")
        
        // Mock other fields as null
        resultSet.getBigDecimal("init_portfolio_net_value") >> null
        resultSet.getBigDecimal("last_price") >> null
        resultSet.getBigDecimal("last_value") >> null
        resultSet.getBigDecimal("risk_unit") >> null
        resultSet.getBigDecimal("stop_percent") >> null
        resultSet.getBigDecimal("highest_after_trade") >> null
        resultSet.getBigDecimal("stop_value_from_highest") >> null
        resultSet.getBigDecimal("last_bbmb") >> null
        resultSet.getBigDecimal("bbmb_adj_percent") >> null
        resultSet.getBigDecimal("stop_value_from_bbmb") >> null
        resultSet.getString("expand_or_contract") >> null
        resultSet.getBigDecimal("effective_stop_value") >> null
        resultSet.getBigDecimal("pnl_value") >> null
        resultSet.getBigDecimal("pnl_percent") >> null
        resultSet.getTimestamp("created_date") >> Timestamp.valueOf(LocalDateTime.now())
        resultSet.getTimestamp("updated_date") >> Timestamp.valueOf(LocalDateTime.now())
    }
}
