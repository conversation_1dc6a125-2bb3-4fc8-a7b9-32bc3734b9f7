2025-06-08 22:08:50 [Test worker] INFO  c.i.service.PositionsService - Creating new position: CreatePositionRequest{symbol='AAPL', position=100, side=BUY, tradePrice=150.25, initPortfolioNetValue=100000, riskUnit=1000, stopPercent=0.02, bbmbAdjPercent=null}
2025-06-08 22:08:50 [Test worker] INFO  c.i.service.PositionsService - Creating new position: CreatePositionRequest{symbol='INVALID', position=100, side=BUY, tradePrice=150.25, initPortfolioNetValue=null, riskUnit=null, stopPercent=null, bbmbAdjPercent=null}
2025-06-08 22:08:50 [Test worker] INFO  c.i.service.PositionsService - Updating position ID 1: UpdatePositionRequest{lastPrice=155.00, riskUnit=1200, stopPercent=null, lastBbmb=null, bbmbAdjPercent=null, expandOrContract=null, status=CLOSED}
2025-06-08 22:08:50 [Test worker] INFO  c.i.service.PositionsService - Updating position ID 999: UpdatePositionRequest{lastPrice=155.00, riskUnit=null, stopPercent=null, lastBbmb=null, bbmbAdjPercent=null, expandOrContract=null, status=null}
2025-06-08 22:08:50 [Test worker] INFO  c.i.service.PositionsService - Closing position ID: 1
2025-06-08 22:08:50 [Test worker] INFO  c.i.service.PositionsService - Deleting position ID: 1
2025-06-08 22:08:50 [Test worker] WARN  c.i.service.PositionsService - Position not found for deletion: 1
2025-06-08 22:08:50 [Test worker] INFO  c.i.service.PositionsService - Deleting position ID: 999
2025-06-08 22:08:50 [Test worker] WARN  c.i.service.PositionsService - Position not found for deletion: 999
