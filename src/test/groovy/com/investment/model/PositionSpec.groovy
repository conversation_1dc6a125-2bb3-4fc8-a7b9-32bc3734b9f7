package com.investment.model

import spock.lang.Specification

import java.math.BigDecimal

/**
 * Test specification for Position model.
 */
class PositionSpec extends Specification {

    def "should create position with required fields"() {
        given: "position parameters"
        def symbol = "AAPL"
        def position = new BigDecimal("100")
        def side = Position.Side.BUY
        def tradePrice = new BigDecimal("150.25")

        when: "creating a position"
        def pos = new Position(symbol, position, side, tradePrice)

        then: "position should be created correctly"
        pos.symbol == symbol
        pos.position == position
        pos.side == side
        pos.tradePrice == tradePrice
        pos.status == Position.Status.OPEN
        pos.tradeValue == new BigDecimal("15025.00")
        pos.createdDate != null
        pos.updatedDate != null
    }

    def "should update market data and calculate P&L for BUY position"() {
        given: "a BUY position"
        def pos = new Position("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.00"))

        when: "updating with higher price"
        pos.updateMarketData(new BigDecimal("155.00"))

        then: "P&L should be positive"
        pos.lastPrice == new BigDecimal("155.00")
        pos.lastValue == new BigDecimal("15500.00")
        pos.pnlValue == new BigDecimal("500.00")
        pos.pnlPercent.compareTo(new BigDecimal("0.033333")) == 0
        pos.highestAfterTrade == new BigDecimal("155.00")

        when: "updating with lower price"
        pos.updateMarketData(new BigDecimal("145.00"))

        then: "P&L should be negative, but highest should remain"
        pos.lastPrice == new BigDecimal("145.00")
        pos.lastValue == new BigDecimal("14500.00")
        pos.pnlValue == new BigDecimal("-500.00")
        pos.pnlPercent.compareTo(new BigDecimal("-0.033333")) == 0
        pos.highestAfterTrade == new BigDecimal("155.00") // Should not decrease
    }

    def "should update market data and calculate P&L for SELL position"() {
        given: "a SELL position"
        def pos = new Position("AAPL", new BigDecimal("-100"), Position.Side.SELL, new BigDecimal("150.00"))

        when: "updating with lower price"
        pos.updateMarketData(new BigDecimal("145.00"))

        then: "P&L should be positive for SELL"
        pos.lastPrice == new BigDecimal("145.00")
        pos.lastValue == new BigDecimal("-14500.00")
        pos.pnlValue == new BigDecimal("500.00") // tradeValue - lastValue for SELL
        pos.pnlPercent.compareTo(new BigDecimal("0.033333")) == 0
        pos.highestAfterTrade == new BigDecimal("145.00") // Lowest for SELL positions

        when: "updating with higher price"
        pos.updateMarketData(new BigDecimal("155.00"))

        then: "P&L should be negative, but lowest should remain"
        pos.lastPrice == new BigDecimal("155.00")
        pos.lastValue == new BigDecimal("-15500.00")
        pos.pnlValue == new BigDecimal("-500.00")
        pos.pnlPercent.compareTo(new BigDecimal("-0.033333")) == 0
        pos.highestAfterTrade == new BigDecimal("145.00") // Should not increase for SELL
    }

    def "should calculate stop values correctly for BUY position"() {
        given: "a BUY position with stop parameters"
        def pos = new Position("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.00"))
        pos.setStopPercent(new BigDecimal("0.02")) // 2% stop
        pos.setBbmbAdjPercent(new BigDecimal("0.01")) // 1% BBMB adjustment

        when: "updating market data"
        pos.updateMarketData(new BigDecimal("155.00"))
        pos.setLastBbmb(new BigDecimal("152.00"))
        pos.updateStopValues() // Recalculate after setting BBMB

        then: "stop values should be calculated"
        pos.stopValueFromHighest == new BigDecimal("151.90") // 155 * (1 - 0.02)
        pos.stopValueFromBbmb == new BigDecimal("150.48") // 152 * (1 - 0.01)
        pos.effectiveStopValue == new BigDecimal("151.90") // Higher value for BUY (more conservative)
    }

    def "should calculate stop values correctly for SELL position"() {
        given: "a SELL position with stop parameters"
        def pos = new Position("AAPL", new BigDecimal("-100"), Position.Side.SELL, new BigDecimal("150.00"))
        pos.setStopPercent(new BigDecimal("0.02")) // 2% stop
        pos.setBbmbAdjPercent(new BigDecimal("0.01")) // 1% BBMB adjustment

        when: "updating market data"
        pos.updateMarketData(new BigDecimal("145.00"))
        pos.setLastBbmb(new BigDecimal("148.00"))
        pos.updateStopValues() // Recalculate after setting BBMB

        then: "stop values should be calculated"
        pos.stopValueFromHighest == new BigDecimal("147.90") // 145 * (1 + 0.02)
        pos.stopValueFromBbmb == new BigDecimal("149.48") // 148 * (1 + 0.01)
        pos.effectiveStopValue == new BigDecimal("147.90") // Lower value for SELL (more conservative)
    }

    def "should determine stop out correctly for BUY position"() {
        given: "a BUY position with effective stop"
        def pos = new Position("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.00"))
        pos.setEffectiveStopValue(new BigDecimal("145.00"))

        expect: "stop out logic to work correctly"
        !pos.shouldStopOut(new BigDecimal("146.00")) // Above stop
        pos.shouldStopOut(new BigDecimal("145.00"))  // At stop
        pos.shouldStopOut(new BigDecimal("144.00"))  // Below stop
    }

    def "should determine stop out correctly for SELL position"() {
        given: "a SELL position with effective stop"
        def pos = new Position("AAPL", new BigDecimal("-100"), Position.Side.SELL, new BigDecimal("150.00"))
        pos.setEffectiveStopValue(new BigDecimal("155.00"))

        expect: "stop out logic to work correctly"
        !pos.shouldStopOut(new BigDecimal("154.00")) // Below stop
        pos.shouldStopOut(new BigDecimal("155.00"))  // At stop
        pos.shouldStopOut(new BigDecimal("156.00"))  // Above stop
    }

    def "should not stop out when no effective stop value"() {
        given: "a position without effective stop"
        def pos = new Position("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.00"))

        expect: "should not stop out"
        !pos.shouldStopOut(new BigDecimal("100.00"))
        !pos.shouldStopOut(new BigDecimal("200.00"))
    }

    def "should not stop out when position is closed"() {
        given: "a closed position with effective stop"
        def pos = new Position("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.00"))
        pos.setEffectiveStopValue(new BigDecimal("145.00"))
        pos.close()

        expect: "should not stop out when closed"
        !pos.shouldStopOut(new BigDecimal("140.00"))
    }

    def "should close position correctly"() {
        given: "an open position"
        def pos = new Position("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.00"))
        def originalUpdatedDate = pos.updatedDate

        when: "closing the position"
        Thread.sleep(1) // Ensure time difference
        pos.close()

        then: "position should be closed"
        pos.status == Position.Status.CLOSED
        pos.updatedDate.isAfter(originalUpdatedDate)
    }

    def "should handle zero trade value in P&L calculation"() {
        given: "a position with zero trade value"
        def pos = new Position("AAPL", new BigDecimal("0"), Position.Side.BUY, new BigDecimal("150.00"))

        when: "updating market data"
        pos.updateMarketData(new BigDecimal("155.00"))

        then: "should not cause division by zero"
        pos.pnlValue == new BigDecimal("0.00")
        pos.pnlPercent == null || pos.pnlPercent == BigDecimal.ZERO
    }

    def "should use only stop from highest when BBMB not available"() {
        given: "a BUY position with only stop percent"
        def pos = new Position("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.00"))
        pos.setStopPercent(new BigDecimal("0.02"))

        when: "updating market data without BBMB"
        pos.updateMarketData(new BigDecimal("155.00"))

        then: "should use only stop from highest"
        pos.stopValueFromHighest == new BigDecimal("151.90")
        pos.stopValueFromBbmb == null
        pos.effectiveStopValue == new BigDecimal("151.90")
    }

    def "should use only stop from BBMB when stop percent not available"() {
        given: "a BUY position with only BBMB parameters"
        def pos = new Position("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.00"))
        pos.setBbmbAdjPercent(new BigDecimal("0.01"))

        when: "updating market data with BBMB"
        pos.updateMarketData(new BigDecimal("155.00"))
        pos.setLastBbmb(new BigDecimal("152.00"))
        pos.updateStopValues() // Recalculate after setting BBMB

        then: "should use only stop from BBMB"
        pos.stopValueFromHighest == null
        pos.stopValueFromBbmb == new BigDecimal("150.48")
        pos.effectiveStopValue == new BigDecimal("150.48")
    }

    def "should have proper toString representation"() {
        given: "a position with data"
        def pos = new Position("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.00"))
        pos.setId(1L)
        pos.updateMarketData(new BigDecimal("155.00"))

        when: "converting to string"
        def str = pos.toString()

        then: "should contain key information"
        str.contains("id=1")
        str.contains("symbol='AAPL'")
        str.contains("position=100")
        str.contains("side=BUY")
        str.contains("status=OPEN")
        str.contains("tradePrice=150.00")
        str.contains("lastPrice=155.00")
    }

    def "should handle enum values correctly"() {
        expect: "enum values to be correct"
        Position.Side.BUY.name() == "BUY"
        Position.Side.SELL.name() == "SELL"
        Position.Status.OPEN.name() == "OPEN"
        Position.Status.CLOSED.name() == "CLOSED"
        Position.ExpandOrContract.EXPANDING.name() == "EXPANDING"
        Position.ExpandOrContract.CONTRACTING.name() == "CONTRACTING"
    }
}
