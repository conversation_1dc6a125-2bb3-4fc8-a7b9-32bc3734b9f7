package com.investment.api.controller;

import com.investment.api.model.ApiResponse;
import com.investment.api.model.BollingerBandRequest;
import com.investment.api.model.BollingerBandResponse;
import com.investment.api.model.DMIRequest;
import com.investment.api.model.DMIResponse;
import com.investment.service.BollingerBandService;
import com.investment.service.DMIService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for technical indicator calculations.
 */
@RestController
@RequestMapping("/api/technical-indicators")
@Tag(name = "Technical Indicators", description = "API for calculating and managing technical indicators")
public class TechnicalIndicatorController {
    
    private static final Logger logger = LoggerFactory.getLogger(TechnicalIndicatorController.class);
    
    private final BollingerBandService bollingerBandService;
    private final DMIService dmiService;

    public TechnicalIndicatorController(BollingerBandService bollingerBandService, DMIService dmiService) {
        this.bollingerBandService = bollingerBandService;
        this.dmiService = dmiService;
    }
    
    /**
     * Calculate and update Bollinger Band technical indicators for all symbols.
     *
     * @param request The calculation request parameters
     * @return Response containing calculation results and statistics
     */
    @PostMapping("/bollinger-bands/calculate")
    @Operation(
        summary = "Calculate Bollinger Band indicators for symbols",
        description = "Calculates and updates Bollinger Band technical indicators (middle band, standard deviation, upper band, lower band) " +
                     "for symbols in the OHLCV database. Supports two modes: " +
                     "1) Specific symbols: Provide a 'symbols' array to calculate for specific stocks " +
                     "2) Pagination: Use 'startIndex', 'endIndex', and 'maxSymbols' to process a range of symbols " +
                     "When 'symbols' is provided, pagination parameters are ignored. " +
                     "Uses optimized SQL window functions for efficient bulk calculation. " +
                     "Supports dry-run mode for validation, custom periods and standard deviation multipliers, " +
                     "and filtering based on minimum data requirements."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Bollinger Band calculation completed successfully",
            content = @Content(schema = @Schema(implementation = BollingerBandResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400", 
            description = "Invalid request parameters"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500", 
            description = "Internal server error during calculation"
        )
    })
    public ResponseEntity<ApiResponse<BollingerBandResponse>> calculateBollingerBands(
            @Valid @RequestBody BollingerBandRequest request) {
        
        try {
            logger.info("Received Bollinger Band calculation request: {}", request);
            
            // Log warning for actual calculation operations
            if (!request.isDryRun()) {
                logger.warn("PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data");
            }
            
            BollingerBandResponse response = bollingerBandService.calculateBollingerBands(request);
            
            String operation = request.isDryRun() ? "Bollinger Band calculation validation" : "Bollinger Band calculation";
            logger.info("{} completed: {}", operation, response.getSummary());
            
            // Determine response message based on status
            String message;
            switch (response.getStatus()) {
                case "success":
                    message = operation + " completed successfully";
                    break;
                case "partial_success":
                    message = operation + " completed with some failures";
                    break;
                case "failed":
                    message = operation + " failed";
                    break;
                default:
                    message = operation + " completed";
            }
            
            return ResponseEntity.ok(ApiResponse.success(message, response));
            
        } catch (Exception e) {
            logger.error("Error during Bollinger Band calculation", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to calculate Bollinger Bands: " + e.getMessage()));
        }
    }

    /**
     * Calculate and update DMI (Directional Movement Index) technical indicators for all symbols.
     *
     * @param request The calculation request parameters
     * @return Response containing calculation results and statistics
     */
    @PostMapping("/dmi/calculate")
    @Operation(
        summary = "Calculate DMI indicators for symbols",
        description = "Calculates and updates DMI (Directional Movement Index) technical indicators (+DI, -DI, ADX) " +
                     "for symbols in the OHLCV database. Supports two modes: " +
                     "1) Specific symbols: Provide a 'symbols' array to calculate for specific stocks " +
                     "2) Pagination: Use 'startIndex', 'endIndex', and 'maxSymbols' to process a range of symbols " +
                     "When 'symbols' is provided, pagination parameters are ignored. " +
                     "DMI is a momentum indicator that measures the strength of price movement in positive and negative directions. " +
                     "Supports incremental calculation, dry-run mode for validation, custom periods, " +
                     "and filtering based on minimum data requirements."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "DMI calculation completed successfully",
            content = @Content(schema = @Schema(implementation = DMIResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid request parameters"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error during calculation"
        )
    })
    public ResponseEntity<ApiResponse<DMIResponse>> calculateDMI(
            @Valid @RequestBody DMIRequest request) {

        try {
            logger.info("Received DMI calculation request: {}", request);

            // Log warning for actual calculation operations
            if (!request.isDryRun()) {
                logger.warn("PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data");
            }

            DMIResponse response = dmiService.calculateDMI(request);

            String operation = request.isDryRun() ? "DMI calculation validation" : "DMI calculation";
            logger.info("{} completed: {}", operation, response.getSummary());

            // Determine response message based on status
            String message;
            switch (response.getStatus()) {
                case "success":
                    message = operation + " completed successfully";
                    break;
                case "partial_success":
                    message = operation + " completed with some failures";
                    break;
                case "failed":
                    message = operation + " failed";
                    break;
                default:
                    message = operation + " completed";
            }

            return ResponseEntity.ok(ApiResponse.success(message, response));

        } catch (Exception e) {
            logger.error("Error during DMI calculation", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to calculate DMI: " + e.getMessage()));
        }
    }

    /**
     * Get information about available technical indicators.
     *
     * @return Information about supported technical indicators
     */
    @GetMapping("/info")
    @Operation(
        summary = "Get information about available technical indicators",
        description = "Returns information about the technical indicators supported by this API, " +
                     "including their parameters and calculation methods."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Technical indicators information retrieved successfully"
        )
    })
    public ResponseEntity<ApiResponse<TechnicalIndicatorInfo>> getTechnicalIndicatorInfo() {
        try {
            TechnicalIndicatorInfo info = new TechnicalIndicatorInfo();
            return ResponseEntity.ok(ApiResponse.success("Technical indicators information", info));
        } catch (Exception e) {
            logger.error("Error retrieving technical indicator information", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to retrieve technical indicator information: " + e.getMessage()));
        }
    }
    
    /**
     * Information about available technical indicators.
     */
    @Schema(description = "Information about available technical indicators")
    public static class TechnicalIndicatorInfo {
        
        @Schema(description = "List of available technical indicators")
        private final IndicatorInfo[] indicators = {
            new IndicatorInfo(
                "bollinger_bands",
                "Bollinger Bands",
                "Volatility indicator consisting of a middle band (SMA) and upper/lower bands based on standard deviation",
                new String[]{"period", "stdDevMultiplier", "minDataPoints", "startIndex", "endIndex", "maxSymbols", "symbols"},
                "POST /api/technical-indicators/bollinger-bands/calculate"
            ),
            new IndicatorInfo(
                "dmi",
                "Directional Movement Index (DMI)",
                "Momentum indicator that measures the strength of price movement in positive and negative directions, includes +DI, -DI, and ADX",
                new String[]{"period", "minDataPoints", "startIndex", "endIndex", "maxSymbols", "symbols"},
                "POST /api/technical-indicators/dmi/calculate"
            )
        };
        
        public IndicatorInfo[] getIndicators() {
            return indicators;
        }
        
        @Schema(description = "Information about a specific technical indicator")
        public static class IndicatorInfo {
            @Schema(description = "Indicator identifier", example = "bollinger_bands")
            private final String id;
            
            @Schema(description = "Human-readable name", example = "Bollinger Bands")
            private final String name;
            
            @Schema(description = "Description of the indicator")
            private final String description;
            
            @Schema(description = "Available parameters for configuration")
            private final String[] parameters;
            
            @Schema(description = "API endpoint for calculation", example = "POST /api/technical-indicators/bollinger-bands/calculate")
            private final String endpoint;
            
            public IndicatorInfo(String id, String name, String description, String[] parameters, String endpoint) {
                this.id = id;
                this.name = name;
                this.description = description;
                this.parameters = parameters;
                this.endpoint = endpoint;
            }
            
            public String getId() { return id; }
            public String getName() { return name; }
            public String getDescription() { return description; }
            public String[] getParameters() { return parameters; }
            public String getEndpoint() { return endpoint; }
        }
    }
}
