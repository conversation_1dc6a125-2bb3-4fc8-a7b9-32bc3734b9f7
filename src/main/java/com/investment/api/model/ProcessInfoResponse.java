package com.investment.api.model;

import com.investment.process.ProcessInfo;
import com.investment.process.ProcessStatus;
import com.investment.process.ProcessType;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * API response model for process information.
 * This class provides a clean API representation of ProcessInfo for REST endpoints.
 */
@Schema(description = "Information about a long-running process")
public class ProcessInfoResponse {
    
    @Schema(description = "Unique process identifier", example = "dmi_calculation_202412151430_0001")
    private final String processId;
    
    @Schema(description = "Type of process")
    private final ProcessType processType;
    
    @Schema(description = "Human-readable description of the process", example = "DMI calculation for all instruments")
    private final String description;
    
    @Schema(description = "Current status of the process")
    private final ProcessStatus status;
    
    @Schema(description = "When the process was started")
    private final LocalDateTime startTime;
    
    @Schema(description = "When the process ended (null if still running)")
    private final LocalDateTime endTime;
    
    @Schema(description = "Progress percentage (0-100)", example = "75")
    private final int progressPercentage;
    
    @Schema(description = "Current operation being performed", example = "Processing symbol AAPL")
    private final String currentOperation;
    
    @Schema(description = "Error message if the process failed")
    private final String errorMessage;
    
    @Schema(description = "Total number of items to process", example = "1000")
    private final int totalItems;
    
    @Schema(description = "Number of items processed so far", example = "750")
    private final int processedItems;
    
    @Schema(description = "Who initiated the process", example = "admin")
    private final String initiatedBy;
    
    @Schema(description = "Duration in seconds", example = "3600")
    private final long durationSeconds;
    
    @Schema(description = "Whether the process is currently running")
    private final boolean isRunning;
    
    @Schema(description = "Whether the process is in a terminal state (completed, failed, or aborted)")
    private final boolean isTerminal;
    
    @Schema(description = "Summary description of the process")
    private final String summary;
    
    /**
     * Create a ProcessInfoResponse from a ProcessInfo object.
     */
    public ProcessInfoResponse(ProcessInfo processInfo) {
        this.processId = processInfo.getProcessId();
        this.processType = processInfo.getProcessType();
        this.description = processInfo.getDescription();
        this.status = processInfo.getStatus();
        this.startTime = processInfo.getStartTime();
        this.endTime = processInfo.getEndTime();
        this.progressPercentage = processInfo.getProgressPercentage();
        this.currentOperation = processInfo.getCurrentOperation();
        this.errorMessage = processInfo.getErrorMessage();
        this.totalItems = processInfo.getTotalItems();
        this.processedItems = processInfo.getProcessedItems();
        this.initiatedBy = processInfo.getInitiatedBy();
        this.durationSeconds = processInfo.getDuration().getSeconds();
        this.isRunning = processInfo.isRunning();
        this.isTerminal = processInfo.isTerminal();
        this.summary = processInfo.getSummary();
    }
    
    // Getters
    public String getProcessId() { return processId; }
    public ProcessType getProcessType() { return processType; }
    public String getDescription() { return description; }
    public ProcessStatus getStatus() { return status; }
    public LocalDateTime getStartTime() { return startTime; }
    public LocalDateTime getEndTime() { return endTime; }
    public int getProgressPercentage() { return progressPercentage; }
    public String getCurrentOperation() { return currentOperation; }
    public String getErrorMessage() { return errorMessage; }
    public int getTotalItems() { return totalItems; }
    public int getProcessedItems() { return processedItems; }
    public String getInitiatedBy() { return initiatedBy; }
    public long getDurationSeconds() { return durationSeconds; }
    public boolean isRunning() { return isRunning; }
    public boolean isTerminal() { return isTerminal; }
    public String getSummary() { return summary; }
}
