2025-06-11 17:20:52 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-06-11 17:20:52 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 27116 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-06-11 17:20:52 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-06-11 17:20:52 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 17:20:53 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-11 17:20:53 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-11 17:20:53 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 17:20:53 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-06-11 17:20:53 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-06-11 17:20:53 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 586 ms
2025-06-11 17:20:53 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-06-11 17:20:53 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-06-11 17:20:53 [main] INFO  c.i.database.DatabaseManager - Current schema version: 5
2025-06-11 17:20:53 [main] INFO  c.i.database.DatabaseManager - Running migrations from version 5 to 6
2025-06-11 17:20:53 [main] INFO  c.i.database.DatabaseManager - Migrating to schema version 6 - creating positions table
2025-06-11 17:20:53 [main] ERROR c.i.database.DatabaseManager - Error initializing database
java.sql.SQLException: java.sql.SQLException: Parser Error: syntax error at or near "GENERATED"
	at org.duckdb.DuckDBPreparedStatement.prepare(DuckDBPreparedStatement.java:121)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:201)
	at com.investment.database.DatabaseManager.migrateToVersion6(DatabaseManager.java:410)
	at com.investment.database.DatabaseManager.runMigrations(DatabaseManager.java:155)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:52)
	at com.investment.config.AppConfig.databaseManager(AppConfig.java:23)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.CGLIB$databaseManager$0(<generated>)
	at com.investment.config.AppConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.databaseManager(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.investment.InvestmentApplication.main(InvestmentApplication.java:23)
Caused by: java.sql.SQLException: Parser Error: syntax error at or near "GENERATED"
	at org.duckdb.DuckDBNative.duckdb_jdbc_prepare(Native Method)
	at org.duckdb.DuckDBPreparedStatement.prepare(DuckDBPreparedStatement.java:115)
	... 61 common frames omitted
2025-06-11 17:20:53 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'instrumentController' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\api\controller\InstrumentController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'symbolValidationService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\SymbolValidationService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
2025-06-11 17:20:53 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-11 17:20:53 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-11 17:20:53 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'instrumentController' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\api\controller\InstrumentController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'symbolValidationService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\SymbolValidationService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.investment.InvestmentApplication.main(InvestmentApplication.java:23)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'symbolValidationService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\SymbolValidationService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:651)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	... 33 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:177)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	... 47 common frames omitted
Caused by: java.lang.RuntimeException: Failed to initialize database
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:55)
	at com.investment.config.AppConfig.databaseManager(AppConfig.java:23)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.CGLIB$databaseManager$0(<generated>)
	at com.investment.config.AppConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.databaseManager(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	... 48 common frames omitted
Caused by: java.sql.SQLException: java.sql.SQLException: Parser Error: syntax error at or near "GENERATED"
	at org.duckdb.DuckDBPreparedStatement.prepare(DuckDBPreparedStatement.java:121)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:201)
	at com.investment.database.DatabaseManager.migrateToVersion6(DatabaseManager.java:410)
	at com.investment.database.DatabaseManager.runMigrations(DatabaseManager.java:155)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:52)
	... 57 common frames omitted
Caused by: java.sql.SQLException: Parser Error: syntax error at or near "GENERATED"
	at org.duckdb.DuckDBNative.duckdb_jdbc_prepare(Native Method)
	at org.duckdb.DuckDBPreparedStatement.prepare(DuckDBPreparedStatement.java:115)
	... 61 common frames omitted
2025-06-11 17:26:33 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-06-11 17:26:33 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 1256 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-06-11 17:26:33 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-06-11 17:26:33 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 17:26:33 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-11 17:26:33 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-11 17:26:33 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 17:26:33 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-06-11 17:26:33 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-06-11 17:26:33 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 489 ms
2025-06-11 17:26:34 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-06-11 17:26:34 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-06-11 17:26:34 [main] INFO  c.i.database.DatabaseManager - Current schema version: 5
2025-06-11 17:26:34 [main] INFO  c.i.database.DatabaseManager - Running migrations from version 5 to 6
2025-06-11 17:26:34 [main] INFO  c.i.database.DatabaseManager - Migrating to schema version 6 - creating positions table
2025-06-11 17:26:34 [main] INFO  c.i.database.DatabaseManager - Positions table and indexes created successfully
2025-06-11 17:26:34 [main] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-06-11 17:26:34 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-11 17:26:34 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/investment-toolkit'
2025-06-11 17:26:34 [main] INFO  c.investment.InvestmentApplication - Started InvestmentApplication in 1.441 seconds (process running for 1.691)
2025-06-11 17:28:52 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 17:28:52 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 17:28:52 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-11 17:28:52 [http-nio-8080-exec-1] DEBUG c.i.a.controller.PositionsController - Retrieving positions with filters - symbol: null, status: null, side: null
2025-06-11 17:28:52 [http-nio-8080-exec-1] DEBUG c.i.service.PositionsService - Retrieving positions with filters - symbol: null, status: null, side: null
2025-06-11 17:28:52 [http-nio-8080-exec-1] DEBUG c.i.service.PositionsService - Retrieved 0 positions
2025-06-11 17:29:19 [http-nio-8080-exec-3] ERROR c.i.a.e.GlobalExceptionHandler - Unhandled exception
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource api/instruments.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-11 17:29:40 [http-nio-8080-exec-4] INFO  c.i.a.controller.PositionsController - Creating new position: CreatePositionRequest{symbol='AAPL', position=100, side=BUY, tradePrice=150.25, initPortfolioNetValue=100000, riskUnit=1000, stopPercent=0.02, bbmbAdjPercent=null}
2025-06-11 17:29:40 [http-nio-8080-exec-4] INFO  c.i.service.PositionsService - Creating new position: CreatePositionRequest{symbol='AAPL', position=100, side=BUY, tradePrice=150.25, initPortfolioNetValue=100000, riskUnit=1000, stopPercent=0.02, bbmbAdjPercent=null}
2025-06-11 17:29:40 [http-nio-8080-exec-4] INFO  c.i.service.PositionsService - Created position with ID: 1
2025-06-11 17:29:40 [http-nio-8080-exec-4] INFO  c.i.a.controller.PositionsController - Created position with ID: 1
2025-06-11 17:30:13 [http-nio-8080-exec-6] DEBUG c.i.a.controller.PositionsController - Retrieving position by ID: 1
2025-06-11 17:30:13 [http-nio-8080-exec-6] DEBUG c.i.service.PositionsService - Retrieving position by ID: 1
2025-06-11 17:31:46 [http-nio-8080-exec-8] DEBUG c.i.a.controller.PositionsController - Updating position ID 1 with price: 155.75
2025-06-11 17:31:46 [http-nio-8080-exec-8] DEBUG c.i.service.PositionsService - Updating position ID 1 with price: 155.75
2025-06-11 17:31:46 [http-nio-8080-exec-8] DEBUG c.i.service.PositionsService - Retrieving position by ID: 1
2025-06-11 17:32:13 [http-nio-8080-exec-10] DEBUG c.i.a.controller.PositionsController - Retrieving positions with filters - symbol: null, status: null, side: null
2025-06-11 17:32:13 [http-nio-8080-exec-10] DEBUG c.i.service.PositionsService - Retrieving positions with filters - symbol: null, status: null, side: null
2025-06-11 17:32:14 [http-nio-8080-exec-10] DEBUG c.i.service.PositionsService - Retrieved 1 positions
2025-06-11 17:33:26 [http-nio-8080-exec-2] DEBUG c.i.a.controller.PositionsController - Retrieving open positions
2025-06-11 17:33:26 [http-nio-8080-exec-2] DEBUG c.i.service.PositionsService - Retrieving positions with filters - symbol: null, status: OPEN, side: null
2025-06-11 17:33:26 [http-nio-8080-exec-2] DEBUG c.i.service.PositionsService - Retrieved 1 positions
2025-06-11 17:36:19 [http-nio-8080-exec-4] INFO  c.i.a.controller.PositionsController - Closing position ID: 1
2025-06-11 17:36:19 [http-nio-8080-exec-4] INFO  c.i.service.PositionsService - Closing position ID: 1
2025-06-11 17:36:19 [http-nio-8080-exec-4] DEBUG c.i.service.PositionsService - Retrieving position by ID: 1
2025-06-11 17:36:19 [http-nio-8080-exec-4] INFO  c.i.service.PositionsService - Closed position ID: 1
2025-06-11 17:36:19 [http-nio-8080-exec-4] INFO  c.i.a.controller.PositionsController - Closed position ID: 1
2025-06-11 17:36:35 [http-nio-8080-exec-6] DEBUG c.i.a.controller.PositionsController - Retrieving open positions
2025-06-11 17:36:35 [http-nio-8080-exec-6] DEBUG c.i.service.PositionsService - Retrieving positions with filters - symbol: null, status: OPEN, side: null
2025-06-11 17:36:35 [http-nio-8080-exec-6] DEBUG c.i.service.PositionsService - Retrieved 0 positions
2025-06-11 17:40:02 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-06-11 17:40:02 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 31624 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-06-11 17:40:02 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-06-11 17:40:02 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-11 17:40:03 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-11 17:40:03 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-11 17:40:03 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 17:40:03 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-06-11 17:40:03 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-06-11 17:40:03 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 451 ms
2025-06-11 17:40:03 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-06-11 17:40:03 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-06-11 17:40:03 [main] INFO  c.i.database.DatabaseManager - Current schema version: 6
2025-06-11 17:40:03 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-11 17:40:03 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/investment-toolkit'
2025-06-11 17:40:03 [main] INFO  c.investment.InvestmentApplication - Started InvestmentApplication in 1.275 seconds (process running for 1.617)
2025-06-11 17:40:20 [SpringApplicationShutdownHook] INFO  c.i.database.DatabaseManager - Database connection closed
