package com.investment.api.model;

import com.investment.process.ProcessManager;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * API response model for the abort all processes operation.
 */
@Schema(description = "Response containing results of aborting all active processes")
public class AbortAllProcessesResponse {
    
    @Schema(description = "Total number of active processes found", example = "5")
    private final int totalActiveProcesses;
    
    @Schema(description = "Number of processes for which abort was requested", example = "5")
    private final int abortRequestsSent;
    
    @Schema(description = "List of process IDs that were successfully sent abort requests")
    private final List<String> abortedProcessIds;
    
    @Schema(description = "List of process IDs that failed to receive abort requests")
    private final List<String> failedToAbortIds;
    
    @Schema(description = "Whether all active processes were successfully sent abort requests")
    private final boolean fullSuccess;
    
    @Schema(description = "When the abort all operation was performed")
    private final LocalDateTime timestamp;
    
    @Schema(description = "Summary message describing the operation result")
    private final String summary;
    
    /**
     * Create response from ProcessManager.AbortAllResult.
     */
    public AbortAllProcessesResponse(ProcessManager.AbortAllResult result) {
        this.totalActiveProcesses = result.getTotalActiveProcesses();
        this.abortRequestsSent = result.getAbortRequestsSent();
        this.abortedProcessIds = result.getAbortedProcessIds();
        this.failedToAbortIds = result.getFailedToAbortIds();
        this.fullSuccess = result.isFullSuccess();
        this.timestamp = LocalDateTime.now();
        this.summary = generateSummary();
    }
    
    private String generateSummary() {
        if (totalActiveProcesses == 0) {
            return "No active processes found to abort";
        } else if (fullSuccess) {
            return String.format("Successfully requested abort for all %d active processes", totalActiveProcesses);
        } else {
            return String.format("Requested abort for %d out of %d active processes (%d failed)", 
                    abortRequestsSent, totalActiveProcesses, failedToAbortIds.size());
        }
    }
    
    // Getters
    public int getTotalActiveProcesses() { return totalActiveProcesses; }
    public int getAbortRequestsSent() { return abortRequestsSent; }
    public List<String> getAbortedProcessIds() { return abortedProcessIds; }
    public List<String> getFailedToAbortIds() { return failedToAbortIds; }
    public boolean isFullSuccess() { return fullSuccess; }
    public LocalDateTime getTimestamp() { return timestamp; }
    public String getSummary() { return summary; }
}
