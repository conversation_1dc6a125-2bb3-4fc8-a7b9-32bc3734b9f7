package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * Request model for DMI (Directional Movement Index) calculation operations.
 */
@Schema(description = "Request parameters for calculating DMI technical indicators")
public class DMIRequest {

    /**
     * Enumeration for different calculation modes.
     */
    @Schema(description = "Calculation mode for DMI processing")
    public enum CalculationMode {
        @Schema(description = "Calculate only for new data points that haven't been processed yet")
        INCREMENTAL,

        @Schema(description = "Calculate from the beginning for all data points, overwriting existing calculations")
        FULL_RECALCULATION,

        @Schema(description = "Skip symbols that already have DMI data (legacy mode)")
        SKIP_EXISTING
    }

    /**
     * Enumeration for different calculation methods.
     */
    @Schema(description = "Calculation method for DMI processing")
    public enum CalculationMethod {
        @Schema(description = "Pure Java implementation for all calculations (default)")
        PURE_JAVA,

        @Schema(description = "Hybrid SQL+Java implementation: SQL for bulk calculations, Java for ADX smoothing")
        HYBRID_SQL_JAVA
    }
    
    @Schema(description = "Number of periods for DMI calculation", 
            example = "14", 
            minimum = "5", 
            maximum = "100")
    @Min(value = 5, message = "Period must be at least 5")
    @Max(value = 100, message = "Period must not exceed 100")
    private int period = 14;
    
    @Schema(description = "If true, only validate and report without updating data", 
            example = "false")
    private boolean dryRun = false;
    
    @Schema(description = "Maximum number of symbols to process (0 = no limit)", 
            example = "0", 
            minimum = "0", 
            maximum = "10000")
    @Min(value = 0, message = "Max symbols must be non-negative")
    @Max(value = 10000, message = "Max symbols must not exceed 10000")
    private int maxSymbols = 0;
    
    @Schema(description = "Minimum number of data points required for calculation (DMI needs 2*period for full calculation)", 
            example = "28", 
            minimum = "10", 
            maximum = "500")
    @Min(value = 10, message = "Minimum data points must be at least 10")
    @Max(value = 500, message = "Minimum data points must not exceed 500")
    private int minDataPoints = 28; // 2 * default period for full DMI calculation
    
    @Schema(description = "Calculation mode: INCREMENTAL (process only new data), FULL_RECALCULATION (recalculate all data), or SKIP_EXISTING (skip symbols with existing data)",
            example = "INCREMENTAL")
    private CalculationMode calculationMode = CalculationMode.INCREMENTAL;

    @Schema(description = "Calculation method: PURE_JAVA (all calculations in Java) or HYBRID_SQL_JAVA (SQL for bulk calculations, Java for ADX smoothing)",
            example = "PURE_JAVA")
    private CalculationMethod calculationMethod = CalculationMethod.HYBRID_SQL_JAVA;

    @Schema(description = "If true, recalculate for symbols that already have DMI data (deprecated - use calculationMode instead)",
            example = "false")
    @Deprecated
    private boolean forceRecalculate = false;

    @Schema(description = "Starting position in the ordered symbol list (0-based index) for pagination",
            example = "0",
            minimum = "0")
    @Min(value = 0, message = "Start index must be non-negative")
    private int startIndex = 0;

    @Schema(description = "Ending position in the ordered symbol list (exclusive) for pagination. If not provided, uses startIndex + maxSymbols",
            example = "100",
            minimum = "1")
    @Min(value = 1, message = "End index must be positive")
    private Integer endIndex = null;

    @Schema(description = "Specific list of stock symbols to calculate DMI for. When provided, pagination parameters are ignored.",
            example = "[\"AAPL\", \"MSFT\", \"GOOGL\"]")
    @Size(max = 1000, message = "Cannot process more than 1000 symbols at once")
    private List<String> symbols = null;

    // Default constructor
    public DMIRequest() {}

    // Constructor with common parameters
    public DMIRequest(int period, boolean dryRun) {
        this.period = period;
        this.dryRun = dryRun;
        this.minDataPoints = period * 2; // DMI needs 2*period for full calculation
    }

    // Constructor with calculation mode
    public DMIRequest(int period, boolean dryRun, CalculationMode calculationMode) {
        this.period = period;
        this.dryRun = dryRun;
        this.calculationMode = calculationMode;
        this.minDataPoints = period * 2;
    }

    // Constructor with pagination parameters
    public DMIRequest(int period, boolean dryRun, CalculationMode calculationMode, int startIndex, Integer endIndex) {
        this.period = period;
        this.dryRun = dryRun;
        this.calculationMode = calculationMode;
        this.minDataPoints = period * 2;
        this.startIndex = startIndex;
        this.endIndex = endIndex;
    }

    // Constructor with specific symbols
    public DMIRequest(int period, boolean dryRun, CalculationMode calculationMode, List<String> symbols) {
        this.period = period;
        this.dryRun = dryRun;
        this.calculationMode = calculationMode;
        this.minDataPoints = period * 2;
        this.symbols = symbols;
    }

    // Getters and setters
    public int getPeriod() {
        return period;
    }

    public void setPeriod(int period) {
        this.period = period;
        // Automatically adjust minDataPoints when period changes
        if (this.minDataPoints < period * 2) {
            this.minDataPoints = period * 2;
        }
    }

    public boolean isDryRun() {
        return dryRun;
    }

    public void setDryRun(boolean dryRun) {
        this.dryRun = dryRun;
    }

    public int getMaxSymbols() {
        return maxSymbols;
    }

    public void setMaxSymbols(int maxSymbols) {
        this.maxSymbols = maxSymbols;
    }

    public int getMinDataPoints() {
        return minDataPoints;
    }

    public void setMinDataPoints(int minDataPoints) {
        this.minDataPoints = minDataPoints;
    }

    public CalculationMode getCalculationMode() {
        return calculationMode;
    }

    public void setCalculationMode(CalculationMode calculationMode) {
        this.calculationMode = calculationMode;
    }

    public CalculationMethod getCalculationMethod() {
        return calculationMethod;
    }

    public void setCalculationMethod(CalculationMethod calculationMethod) {
        this.calculationMethod = calculationMethod;
    }

    @Deprecated
    public boolean isForceRecalculate() {
        return forceRecalculate;
    }

    @Deprecated
    public void setForceRecalculate(boolean forceRecalculate) {
        this.forceRecalculate = forceRecalculate;
        // For backward compatibility, map boolean to enum
        if (forceRecalculate) {
            this.calculationMode = CalculationMode.FULL_RECALCULATION;
        } else {
            this.calculationMode = CalculationMode.SKIP_EXISTING;
        }
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    public Integer getEndIndex() {
        return endIndex;
    }

    public void setEndIndex(Integer endIndex) {
        this.endIndex = endIndex;
    }

    public List<String> getSymbols() {
        return symbols;
    }

    public void setSymbols(List<String> symbols) {
        this.symbols = symbols;
    }

    /**
     * Calculate the effective limit for database queries.
     * @return the number of records to fetch
     */
    public int getEffectiveLimit() {
        if (endIndex != null) {
            return Math.min(endIndex - startIndex, maxSymbols > 0 ? maxSymbols : Integer.MAX_VALUE);
        }
        return maxSymbols > 0 ? maxSymbols : Integer.MAX_VALUE;
    }

    /**
     * Calculate the effective end index.
     * @return the calculated end index
     */
    public int getEffectiveEndIndex() {
        if (endIndex != null) {
            return maxSymbols > 0 ? Math.min(endIndex, startIndex + maxSymbols) : endIndex;
        }
        return maxSymbols > 0 ? startIndex + maxSymbols : Integer.MAX_VALUE;
    }

    /**
     * Validate pagination parameters.
     * @throws IllegalArgumentException if validation fails
     */
    public void validatePagination() {
        if (startIndex < 0) {
            throw new IllegalArgumentException("Start index must be non-negative, got: " + startIndex);
        }

        if (endIndex != null) {
            if (endIndex <= 0) {
                throw new IllegalArgumentException("End index must be positive, got: " + endIndex);
            }

            if (endIndex <= startIndex) {
                throw new IllegalArgumentException(
                    String.format("End index (%d) must be greater than start index (%d)", endIndex, startIndex));
            }
        }

        if (maxSymbols < 0) {
            throw new IllegalArgumentException("Max symbols must be non-negative, got: " + maxSymbols);
        }
    }

    /**
     * Check if specific symbols are provided for processing.
     * @return true if symbols list is provided and not empty
     */
    public boolean hasSpecificSymbols() {
        return symbols != null && !symbols.isEmpty();
    }

    @Override
    public String toString() {
        return "DMIRequest{" +
                "period=" + period +
                ", dryRun=" + dryRun +
                ", maxSymbols=" + maxSymbols +
                ", minDataPoints=" + minDataPoints +
                ", calculationMode=" + calculationMode +
                ", calculationMethod=" + calculationMethod +
                ", startIndex=" + startIndex +
                ", endIndex=" + endIndex +
                ", symbols=" + (symbols != null ? symbols.size() + " symbols" : "null") +
                ", forceRecalculate=" + forceRecalculate + " (deprecated)" +
                '}';
    }
}
