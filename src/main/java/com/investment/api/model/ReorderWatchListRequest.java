package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * Request model for bulk reordering of watch list items.
 */
@Schema(description = "Request to reorder watch list items by updating display indexes")
public class ReorderWatchListRequest {

    @NotNull(message = "ID to index mapping is required")
    @NotEmpty(message = "At least one item must be provided for reordering")
    @Schema(description = "Map of watch list item ID to new display index", 
            example = "{\"1\": 0, \"2\": 1, \"3\": 2}", required = true)
    private Map<Long, Integer> idToIndexMap;

    /**
     * Default constructor.
     */
    public ReorderWatchListRequest() {
    }

    /**
     * Constructor with mapping.
     */
    public ReorderWatchListRequest(Map<Long, Integer> idToIndexMap) {
        this.idToIndexMap = idToIndexMap;
    }

    /**
     * Validate the reorder request.
     */
    public void validate() {
        if (idToIndexMap == null || idToIndexMap.isEmpty()) {
            throw new IllegalArgumentException("ID to index mapping cannot be null or empty");
        }

        // Check for negative indexes
        for (Map.Entry<Long, Integer> entry : idToIndexMap.entrySet()) {
            if (entry.getKey() == null) {
                throw new IllegalArgumentException("Watch list item ID cannot be null");
            }
            if (entry.getValue() == null || entry.getValue() < 0) {
                throw new IllegalArgumentException("Display index must be non-negative for ID: " + entry.getKey());
            }
        }

        // Check for duplicate indexes
        long uniqueIndexCount = idToIndexMap.values().stream().distinct().count();
        if (uniqueIndexCount != idToIndexMap.size()) {
            throw new IllegalArgumentException("Duplicate display indexes are not allowed");
        }
    }

    // Getters and Setters

    public Map<Long, Integer> getIdToIndexMap() {
        return idToIndexMap;
    }

    public void setIdToIndexMap(Map<Long, Integer> idToIndexMap) {
        this.idToIndexMap = idToIndexMap;
    }

    @Override
    public String toString() {
        return "ReorderWatchListRequest{" +
                "idToIndexMap=" + idToIndexMap +
                '}';
    }
}
