package com.investment.api.model;

import com.investment.model.Position;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

/**
 * Request model for updating an existing position.
 */
@Schema(description = "Request parameters for updating an existing portfolio position")
public class UpdatePositionRequest {
    
    @Schema(description = "Current market price for the instrument", 
            example = "155.75")
    @DecimalMin(value = "0.000001", message = "Last price must be positive")
    @DecimalMax(value = "999999999.999999", message = "Last price is too large")
    private BigDecimal lastPrice;
    
    @Schema(description = "Risk unit size for position sizing calculations", 
            example = "1000.00")
    @DecimalMin(value = "0.0", message = "Risk unit must be non-negative")
    @DecimalMax(value = "999999999.999999", message = "Risk unit is too large")
    private BigDecimal riskUnit;
    
    @Schema(description = "Stop loss percentage (e.g., 0.02 for 2%)", 
            example = "0.02")
    @DecimalMin(value = "0.0", message = "Stop percent must be non-negative")
    @DecimalMax(value = "1.0", message = "Stop percent must not exceed 100%")
    private BigDecimal stopPercent;
    
    @Schema(description = "Last Bollinger Band Middle Band value", 
            example = "152.30")
    @DecimalMin(value = "0.000001", message = "Last BBMB must be positive")
    @DecimalMax(value = "999999999.999999", message = "Last BBMB is too large")
    private BigDecimal lastBbmb;
    
    @Schema(description = "Bollinger Band Middle Band adjustment percentage", 
            example = "0.01")
    @DecimalMin(value = "0.0", message = "BBMB adjustment percent must be non-negative")
    @DecimalMax(value = "1.0", message = "BBMB adjustment percent must not exceed 100%")
    private BigDecimal bbmbAdjPercent;
    
    @Schema(description = "Whether distance between last price and BBMB is expanding or contracting")
    private Position.ExpandOrContract expandOrContract;
    
    @Schema(description = "Position status")
    private Position.Status status;
    
    // Default constructor
    public UpdatePositionRequest() {}
    
    // Getters and Setters
    public BigDecimal getLastPrice() { return lastPrice; }
    public void setLastPrice(BigDecimal lastPrice) { this.lastPrice = lastPrice; }
    
    public BigDecimal getRiskUnit() { return riskUnit; }
    public void setRiskUnit(BigDecimal riskUnit) { this.riskUnit = riskUnit; }
    
    public BigDecimal getStopPercent() { return stopPercent; }
    public void setStopPercent(BigDecimal stopPercent) { this.stopPercent = stopPercent; }
    
    public BigDecimal getLastBbmb() { return lastBbmb; }
    public void setLastBbmb(BigDecimal lastBbmb) { this.lastBbmb = lastBbmb; }
    
    public BigDecimal getBbmbAdjPercent() { return bbmbAdjPercent; }
    public void setBbmbAdjPercent(BigDecimal bbmbAdjPercent) { this.bbmbAdjPercent = bbmbAdjPercent; }
    
    public Position.ExpandOrContract getExpandOrContract() { return expandOrContract; }
    public void setExpandOrContract(Position.ExpandOrContract expandOrContract) { this.expandOrContract = expandOrContract; }
    
    public Position.Status getStatus() { return status; }
    public void setStatus(Position.Status status) { this.status = status; }
    
    /**
     * Check if any field is provided for update.
     */
    public boolean hasUpdates() {
        return lastPrice != null || riskUnit != null || stopPercent != null || 
               lastBbmb != null || bbmbAdjPercent != null || expandOrContract != null || status != null;
    }
    
    @Override
    public String toString() {
        return String.format("UpdatePositionRequest{lastPrice=%s, riskUnit=%s, stopPercent=%s, lastBbmb=%s, bbmbAdjPercent=%s, expandOrContract=%s, status=%s}",
                lastPrice, riskUnit, stopPercent, lastBbmb, bbmbAdjPercent, expandOrContract, status);
    }
}
