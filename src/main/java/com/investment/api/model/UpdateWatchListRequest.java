package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * Request model for updating an existing watch list item.
 */
@Schema(description = "Request to update an existing watch list item")
public class UpdateWatchListRequest {

    @Min(value = 0, message = "Display index must be non-negative")
    @Schema(description = "Display index for custom ordering", example = "2")
    private Integer displayIndex;

    @Size(max = 128, message = "Remarks cannot exceed 128 characters")
    @Schema(description = "Optional notes/comments about the symbol", example = "Updated analysis")
    private String remarks;

    @Schema(description = "1-month performance percentage", example = "0.0523")
    private BigDecimal oneMonthPerf;

    @Schema(description = "3-month performance percentage", example = "0.1245")
    private BigDecimal threeMonthPerf;

    @Schema(description = "6-month performance percentage", example = "0.2187")
    private BigDecimal sixMonthPerf;

    /**
     * Default constructor.
     */
    public UpdateWatchListRequest() {
    }

    /**
     * Check if any fields are provided for update.
     */
    public boolean hasUpdates() {
        return displayIndex != null || remarks != null || 
               oneMonthPerf != null || threeMonthPerf != null || sixMonthPerf != null;
    }

    /**
     * Check if performance metrics are being updated.
     */
    public boolean hasPerformanceUpdates() {
        return oneMonthPerf != null || threeMonthPerf != null || sixMonthPerf != null;
    }

    // Getters and Setters

    public Integer getDisplayIndex() {
        return displayIndex;
    }

    public void setDisplayIndex(Integer displayIndex) {
        this.displayIndex = displayIndex;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public BigDecimal getOneMonthPerf() {
        return oneMonthPerf;
    }

    public void setOneMonthPerf(BigDecimal oneMonthPerf) {
        this.oneMonthPerf = oneMonthPerf;
    }

    public BigDecimal getThreeMonthPerf() {
        return threeMonthPerf;
    }

    public void setThreeMonthPerf(BigDecimal threeMonthPerf) {
        this.threeMonthPerf = threeMonthPerf;
    }

    public BigDecimal getSixMonthPerf() {
        return sixMonthPerf;
    }

    public void setSixMonthPerf(BigDecimal sixMonthPerf) {
        this.sixMonthPerf = sixMonthPerf;
    }

    @Override
    public String toString() {
        return "UpdateWatchListRequest{" +
                "displayIndex=" + displayIndex +
                ", remarks='" + remarks + '\'' +
                ", oneMonthPerf=" + oneMonthPerf +
                ", threeMonthPerf=" + threeMonthPerf +
                ", sixMonthPerf=" + sixMonthPerf +
                '}';
    }
}
