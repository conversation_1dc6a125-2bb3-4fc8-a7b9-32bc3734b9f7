2025-06-07 19:43:58 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:43:58 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:43:58 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Bollinger Band calculation completed: Bollinger Band calculation completed: 10 symbols processed, 500 records updated in 1000ms
2025-06-07 19:43:58 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=3 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:43:58 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:43:58 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during Bollinger Band calculation
java.lang.NullPointerException: Cannot invoke "com.investment.api.model.BollingerBandResponse.getSummary()" because "response" is null
	at com.investment.api.controller.TechnicalIndicatorController.calculateBollingerBands(TechnicalIndicatorController.java:86)
	at com.investment.api.controller.TechnicalIndicatorController$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.BollingerBandControllerSpec.$spock_feature_0_1(BollingerBandControllerSpec.groovy:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:43:59 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=5, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=10, endIndex=20, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:43:59 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:43:59 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during Bollinger Band calculation
java.lang.NullPointerException: Cannot invoke "com.investment.api.model.BollingerBandResponse.getSummary()" because "response" is null
	at com.investment.api.controller.TechnicalIndicatorController.calculateBollingerBands(TechnicalIndicatorController.java:86)
	at com.investment.api.controller.TechnicalIndicatorController$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.BollingerBandControllerSpec.$spock_feature_0_2(BollingerBandControllerSpec.groovy:84)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:43:59 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=5, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=10, endIndex=20, symbols=2 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:43:59 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:43:59 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during Bollinger Band calculation
java.lang.NullPointerException: Cannot invoke "com.investment.api.model.BollingerBandResponse.getSummary()" because "response" is null
	at com.investment.api.controller.TechnicalIndicatorController.calculateBollingerBands(TechnicalIndicatorController.java:86)
	at com.investment.api.controller.TechnicalIndicatorController$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.BollingerBandControllerSpec.$spock_feature_0_3(BollingerBandControllerSpec.groovy:113)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:43:59 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=true, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:43:59 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Bollinger Band calculation validation completed: Dry run completed: 5 symbols would be processed, 250 records would be updated
2025-06-07 19:43:59 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:43:59 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:43:59 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during Bollinger Band calculation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.BollingerBandControllerSpec$__spock_feature_0_5_closure4.doCall(BollingerBandControllerSpec.groovy:151)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.BollingerBandService$SpockMock$1527061596.calculateBollingerBands(Unknown Source)
	at com.investment.api.controller.TechnicalIndicatorController.calculateBollingerBands(TechnicalIndicatorController.java:83)
	at com.investment.api.controller.TechnicalIndicatorController$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.BollingerBandControllerSpec.$spock_feature_0_5(BollingerBandControllerSpec.groovy:154)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:43:59 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=0 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:43:59 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:43:59 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during Bollinger Band calculation
java.lang.NullPointerException: Cannot invoke "com.investment.api.model.BollingerBandResponse.getSummary()" because "response" is null
	at com.investment.api.controller.TechnicalIndicatorController.calculateBollingerBands(TechnicalIndicatorController.java:86)
	at com.investment.api.controller.TechnicalIndicatorController$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.BollingerBandControllerSpec.$spock_feature_0_6(BollingerBandControllerSpec.groovy:176)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:43:59 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=30, stdDevMultiplier=2.5, dryRun=false, maxSymbols=50, minDataPoints=35, calculationMode=FULL_RECALCULATION, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:43:59 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:43:59 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Bollinger Band calculation completed: Bollinger Band calculation completed: 50 symbols processed, 2500 records updated in 2000ms
2025-06-07 19:45:25 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:45:25 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:45:25 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Bollinger Band calculation completed: Bollinger Band calculation completed: 10 symbols processed, 500 records updated in 1000ms
2025-06-07 19:45:25 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=3 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:45:25 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:45:25 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during Bollinger Band calculation
java.lang.NullPointerException: Cannot invoke "com.investment.api.model.BollingerBandResponse.getSummary()" because "response" is null
	at com.investment.api.controller.TechnicalIndicatorController.calculateBollingerBands(TechnicalIndicatorController.java:86)
	at com.investment.api.controller.TechnicalIndicatorController$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.BollingerBandControllerSpec.$spock_feature_0_1(BollingerBandControllerSpec.groovy:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:45:26 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=5, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=10, endIndex=20, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:45:26 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:45:26 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during Bollinger Band calculation
java.lang.NullPointerException: Cannot invoke "com.investment.api.model.BollingerBandResponse.getSummary()" because "response" is null
	at com.investment.api.controller.TechnicalIndicatorController.calculateBollingerBands(TechnicalIndicatorController.java:86)
	at com.investment.api.controller.TechnicalIndicatorController$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.BollingerBandControllerSpec.$spock_feature_0_2(BollingerBandControllerSpec.groovy:84)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:45:26 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=5, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=10, endIndex=20, symbols=2 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:45:26 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:45:26 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during Bollinger Band calculation
java.lang.NullPointerException: Cannot invoke "com.investment.api.model.BollingerBandResponse.getSummary()" because "response" is null
	at com.investment.api.controller.TechnicalIndicatorController.calculateBollingerBands(TechnicalIndicatorController.java:86)
	at com.investment.api.controller.TechnicalIndicatorController$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.BollingerBandControllerSpec.$spock_feature_0_3(BollingerBandControllerSpec.groovy:113)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:45:26 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=true, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:45:26 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Bollinger Band calculation validation completed: Dry run completed: 5 symbols would be processed, 250 records would be updated
2025-06-07 19:45:26 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:45:26 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:45:26 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during Bollinger Band calculation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.BollingerBandControllerSpec$__spock_feature_0_5_closure4.doCall(BollingerBandControllerSpec.groovy:151)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.BollingerBandService$SpockMock$980734146.calculateBollingerBands(Unknown Source)
	at com.investment.api.controller.TechnicalIndicatorController.calculateBollingerBands(TechnicalIndicatorController.java:83)
	at com.investment.api.controller.TechnicalIndicatorController$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.BollingerBandControllerSpec.$spock_feature_0_5(BollingerBandControllerSpec.groovy:154)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:45:26 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=0 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:45:26 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:45:26 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during Bollinger Band calculation
java.lang.NullPointerException: Cannot invoke "com.investment.api.model.BollingerBandResponse.getSummary()" because "response" is null
	at com.investment.api.controller.TechnicalIndicatorController.calculateBollingerBands(TechnicalIndicatorController.java:86)
	at com.investment.api.controller.TechnicalIndicatorController$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.BollingerBandControllerSpec.$spock_feature_0_6(BollingerBandControllerSpec.groovy:176)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:45:26 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=30, stdDevMultiplier=2.5, dryRun=false, maxSymbols=50, minDataPoints=35, calculationMode=FULL_RECALCULATION, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:45:26 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:45:26 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Bollinger Band calculation completed: Bollinger Band calculation completed: 50 symbols processed, 2500 records updated in 2000ms
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:01 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Bollinger Band calculation completed: Bollinger Band calculation completed: 10 symbols processed, 500 records updated in 1000ms
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=3 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:49:01 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Bollinger Band calculation completed: Bollinger Band calculation completed: 3 symbols processed, 150 records updated in 800ms
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=5, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=10, endIndex=20, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:01 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Bollinger Band calculation completed: Bollinger Band calculation completed: 5 symbols processed, 250 records updated in 600ms
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=5, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=10, endIndex=20, symbols=2 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:49:01 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Bollinger Band calculation completed: Bollinger Band calculation completed: 2 symbols processed, 100 records updated in 400ms
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=true, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Bollinger Band calculation validation completed: Dry run completed: 5 symbols would be processed, 250 records would be updated
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:01 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:01 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during Bollinger Band calculation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.BollingerBandControllerSpec$__spock_feature_0_5_closure1.doCall(BollingerBandControllerSpec.groovy:136)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.BollingerBandService$SpockMock$124032176.calculateBollingerBands(Unknown Source)
	at com.investment.api.controller.TechnicalIndicatorController.calculateBollingerBands(TechnicalIndicatorController.java:83)
	at com.investment.api.controller.TechnicalIndicatorController$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.BollingerBandControllerSpec.$spock_feature_0_5(BollingerBandControllerSpec.groovy:139)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, startIndex=0, endIndex=null, symbols=0 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:49:01 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Bollinger Band calculation completed: Bollinger Band calculation completed: 0 symbols processed, 0 records updated in 100ms
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received Bollinger Band calculation request: BollingerBandRequest{period=30, stdDevMultiplier=2.5, dryRun=false, maxSymbols=50, minDataPoints=35, calculationMode=FULL_RECALCULATION, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:01 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:01 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Bollinger Band calculation completed: Bollinger Band calculation completed: 50 symbols processed, 2500 records updated in 2000ms
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 10 symbols processed (range: 0-9 of 100 total), 500 records updated in 1000ms
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=true, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation validation completed: Dry run completed: 5 symbols would be processed (range: 0-4 of 50 total), 250 records would be updated
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=FULL_RECALCULATION, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 8 symbols processed (range: 0-9 of 10 total), 400 records updated in 1200ms
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:54 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during DMI calculation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.DMIControllerSpec$__spock_feature_0_3_closure1.doCall(DMIControllerSpec.groovy:93)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.DMIService$SpockMock$579382598.calculateDMI(Unknown Source)
	at com.investment.api.controller.TechnicalIndicatorController.calculateDMI(TechnicalIndicatorController.java:157)
	at com.investment.api.controller.TechnicalIndicatorController$calculateDMI.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.DMIControllerSpec.$spock_feature_0_3(DMIControllerSpec.groovy:96)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 5 symbols processed (range: 0-4 of 10 total), 250 records updated in 500ms
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=FULL_RECALCULATION, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 10 symbols processed (range: 0-9 of 20 total), 500 records updated in 1000ms
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=SKIP_EXISTING, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 3 symbols processed (range: 0-4 of 15 total), 150 records updated in 300ms
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=21, dryRun=false, maxSymbols=50, minDataPoints=45, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 50 symbols processed (range: 0-49 of 100 total), 2500 records updated in 2000ms
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 5 symbols processed (range: 0-6 of 7 total), 250 records updated in 800ms
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 10 symbols processed (range: 0-9 of 20 total), 500 records updated in 1000ms
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=true, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation validation completed: Dry run completed: 10 symbols would be processed (range: 0-9 of 20 total), 500 records would be updated
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=3 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:54 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during DMI calculation
java.lang.NullPointerException: Cannot invoke "com.investment.api.model.DMIResponse.getSummary()" because "response" is null
	at com.investment.api.controller.TechnicalIndicatorController.calculateDMI(TechnicalIndicatorController.java:160)
	at com.investment.api.controller.TechnicalIndicatorController$calculateDMI.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.DMIControllerSpec.$spock_feature_0_9(DMIControllerSpec.groovy:256)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=5, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=10, endIndex=20, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:54 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during DMI calculation
java.lang.NullPointerException: Cannot invoke "com.investment.api.model.DMIResponse.getSummary()" because "response" is null
	at com.investment.api.controller.TechnicalIndicatorController.calculateDMI(TechnicalIndicatorController.java:160)
	at com.investment.api.controller.TechnicalIndicatorController$calculateDMI.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.DMIControllerSpec.$spock_feature_0_10(DMIControllerSpec.groovy:286)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=5, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=10, endIndex=20, symbols=2 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:54 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during DMI calculation
java.lang.NullPointerException: Cannot invoke "com.investment.api.model.DMIResponse.getSummary()" because "response" is null
	at com.investment.api.controller.TechnicalIndicatorController.calculateDMI(TechnicalIndicatorController.java:160)
	at com.investment.api.controller.TechnicalIndicatorController$calculateDMI.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.DMIControllerSpec.$spock_feature_0_11(DMIControllerSpec.groovy:318)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:49:54 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=0 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:49:54 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:49:54 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during DMI calculation
java.lang.NullPointerException: Cannot invoke "com.investment.api.model.DMIResponse.getSummary()" because "response" is null
	at com.investment.api.controller.TechnicalIndicatorController.calculateDMI(TechnicalIndicatorController.java:160)
	at com.investment.api.controller.TechnicalIndicatorController$calculateDMI.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.DMIControllerSpec.$spock_feature_0_12(DMIControllerSpec.groovy:344)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 10 symbols processed (range: 0-9 of 100 total), 500 records updated in 1000ms
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=true, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation validation completed: Dry run completed: 5 symbols would be processed (range: 0-4 of 50 total), 250 records would be updated
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=FULL_RECALCULATION, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 8 symbols processed (range: 0-9 of 10 total), 400 records updated in 1200ms
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:10 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during DMI calculation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.DMIControllerSpec$__spock_feature_0_3_closure1.doCall(DMIControllerSpec.groovy:93)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.DMIService$SpockMock$1696955669.calculateDMI(Unknown Source)
	at com.investment.api.controller.TechnicalIndicatorController.calculateDMI(TechnicalIndicatorController.java:157)
	at com.investment.api.controller.TechnicalIndicatorController$calculateDMI.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.DMIControllerSpec.$spock_feature_0_3(DMIControllerSpec.groovy:96)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 5 symbols processed (range: 0-4 of 10 total), 250 records updated in 500ms
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=FULL_RECALCULATION, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 10 symbols processed (range: 0-9 of 20 total), 500 records updated in 1000ms
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=SKIP_EXISTING, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 3 symbols processed (range: 0-4 of 15 total), 150 records updated in 300ms
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=21, dryRun=false, maxSymbols=50, minDataPoints=45, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 50 symbols processed (range: 0-49 of 100 total), 2500 records updated in 2000ms
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 5 symbols processed (range: 0-6 of 7 total), 250 records updated in 800ms
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 10 symbols processed (range: 0-9 of 20 total), 500 records updated in 1000ms
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=true, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation validation completed: Dry run completed: 10 symbols would be processed (range: 0-9 of 20 total), 500 records would be updated
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=3 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 3 symbols processed (range: 0-2 of 3 total), 150 records updated in 800ms
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=5, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=10, endIndex=20, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 5 symbols processed (range: 10-14 of 100 total), 250 records updated in 600ms
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=5, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=10, endIndex=20, symbols=2 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 2 symbols processed (range: 0-1 of 2 total), 100 records updated in 400ms
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=0 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:51:10 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:10 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 0 symbols processed (range: 0--1 of 0 total), 0 records updated in 100ms
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 10 symbols processed (range: 0-9 of 100 total), 500 records updated in 1000ms
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=true, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation validation completed: Dry run completed: 5 symbols would be processed (range: 0-4 of 50 total), 250 records would be updated
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=FULL_RECALCULATION, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 8 symbols processed (range: 0-9 of 10 total), 400 records updated in 1200ms
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:37 [Test worker] ERROR c.i.a.c.TechnicalIndicatorController - Error during DMI calculation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.DMIControllerSpec$__spock_feature_0_3_closure1.doCall(DMIControllerSpec.groovy:93)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.DMIService$SpockMock$780545486.calculateDMI(Unknown Source)
	at com.investment.api.controller.TechnicalIndicatorController.calculateDMI(TechnicalIndicatorController.java:157)
	at com.investment.api.controller.TechnicalIndicatorController$calculateDMI.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.DMIControllerSpec.$spock_feature_0_3(DMIControllerSpec.groovy:96)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 5 symbols processed (range: 0-4 of 10 total), 250 records updated in 500ms
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=FULL_RECALCULATION, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 10 symbols processed (range: 0-9 of 20 total), 500 records updated in 1000ms
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=SKIP_EXISTING, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 3 symbols processed (range: 0-4 of 15 total), 150 records updated in 300ms
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=21, dryRun=false, maxSymbols=50, minDataPoints=45, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 50 symbols processed (range: 0-49 of 100 total), 2500 records updated in 2000ms
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 5 symbols processed (range: 0-6 of 7 total), 250 records updated in 800ms
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 10 symbols processed (range: 0-9 of 20 total), 500 records updated in 1000ms
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=true, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation validation completed: Dry run completed: 10 symbols would be processed (range: 0-9 of 20 total), 500 records would be updated
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=3 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 3 symbols processed (range: 0-2 of 3 total), 150 records updated in 800ms
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=5, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=10, endIndex=20, symbols=null, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 5 symbols processed (range: 10-14 of 100 total), 250 records updated in 600ms
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=5, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=10, endIndex=20, symbols=2 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 2 symbols processed (range: 0-1 of 2 total), 100 records updated in 400ms
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - Received DMI calculation request: DMIRequest{period=14, dryRun=false, maxSymbols=0, minDataPoints=28, calculationMode=INCREMENTAL, calculationMethod=HYBRID_SQL_JAVA, startIndex=0, endIndex=null, symbols=0 symbols, forceRecalculate=false (deprecated)}
2025-06-07 19:51:37 [Test worker] WARN  c.i.a.c.TechnicalIndicatorController - PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data
2025-06-07 19:51:37 [Test worker] INFO  c.i.a.c.TechnicalIndicatorController - DMI calculation completed: DMI calculation completed: 0 symbols processed (range: 0--1 of 0 total), 0 records updated in 100ms
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072329_0001 [DMI Calculation] - Test DMI calculation
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072329_0001 [DMI Calculation] - Test process
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072329_0002 [DMI Calculation] - Test process
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072329_0003 [DMI Calculation] - Test process
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072329_0001 [DMI Calculation] - Process 1
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072329_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072329_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072329_0001 [DMI Calculation] - Test process
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: dmi_calculation_202506072329_0001
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072329_0001 [DMI Calculation] - Process 1
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072329_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072329_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for 2 active processes
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: ohlcv_refresh_202506072329_0002
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Abort requested for process: ohlcv_refresh_202506072329_0002 [OHLCV Data Refresh]
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: dmi_calculation_202506072329_0001
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Abort requested for process: dmi_calculation_202506072329_0001 [DMI Calculation]
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Abort all processes completed: 2/2 processes abort requested
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072329_0001 [DMI Calculation] - DMI process
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072329_0002 [OHLCV Data Refresh] - OHLCV process
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072329_0003 [SEC Data Synchronization] - SEC process
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072329_0001 [DMI Calculation] - Process 1
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072329_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072329_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072329_0001 [DMI Calculation] - Process 1
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072329_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072329_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Cleaned up 2 completed processes
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072329_0001 [DMI Calculation] - DMI 1
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072329_0002 [DMI Calculation] - DMI 2
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072329_0003 [OHLCV Data Refresh] - OHLCV 1
2025-06-07 23:29:09 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072329_0004 [SEC Data Synchronization] - SEC 1
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072339_0001 [DMI Calculation] - Process 1
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072339_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072339_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:39:24 [Test worker] INFO  c.i.api.controller.ProcessController - Received request to abort all active processes
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for 2 active processes
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: dmi_calculation_202506072339_0001
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Abort requested for process: dmi_calculation_202506072339_0001 [DMI Calculation]
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: ohlcv_refresh_202506072339_0002
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Abort requested for process: ohlcv_refresh_202506072339_0002 [OHLCV Data Refresh]
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Abort all processes completed: 2/2 processes abort requested
2025-06-07 23:39:24 [Test worker] INFO  c.i.api.controller.ProcessController - Abort all processes completed: Successfully requested abort for all 2 active processes
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072339_0001 [DMI Calculation] - Process 1
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072339_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072339_0001 [DMI Calculation] - Process 1
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072339_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072339_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072339_0001 [DMI Calculation] - DMI process
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072339_0002 [OHLCV Data Refresh] - OHLCV process
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072339_0003 [SEC Data Synchronization] - SEC process
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072339_0001 [DMI Calculation] - Process 1
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072339_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072339_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072339_0001 [DMI Calculation] - Test process
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072339_0001 [DMI Calculation] - Test process
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: dmi_calculation_202506072339_0001
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072339_0001 [DMI Calculation] - DMI 1
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072339_0002 [DMI Calculation] - DMI 2
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072339_0003 [OHLCV Data Refresh] - OHLCV 1
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072339_0004 [SEC Data Synchronization] - SEC 1
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072339_0001 [DMI Calculation] - Process 1
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072339_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072339_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Cleaned up 2 completed processes
2025-06-07 23:39:24 [Test worker] INFO  c.i.api.controller.ProcessController - Received request to abort all active processes
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for 0 active processes
2025-06-07 23:39:24 [Test worker] INFO  c.investment.process.ProcessManager - Abort all processes completed: 0/0 processes abort requested
2025-06-07 23:39:24 [Test worker] INFO  c.i.api.controller.ProcessController - Abort all processes completed: No active processes found to abort
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072342_0001 [DMI Calculation] - Process 1
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072342_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072342_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:42:06 [Test worker] INFO  c.i.api.controller.ProcessController - Received request to abort all active processes
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for 2 active processes
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: dmi_calculation_202506072342_0001
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Abort requested for process: dmi_calculation_202506072342_0001 [DMI Calculation]
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: ohlcv_refresh_202506072342_0002
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Abort requested for process: ohlcv_refresh_202506072342_0002 [OHLCV Data Refresh]
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Abort all processes completed: 2/2 processes abort requested
2025-06-07 23:42:06 [Test worker] INFO  c.i.api.controller.ProcessController - Abort all processes completed: Successfully requested abort for all 2 active processes
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072342_0001 [DMI Calculation] - Process 1
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072342_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072342_0001 [DMI Calculation] - Process 1
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072342_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072342_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072342_0001 [DMI Calculation] - DMI process
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072342_0002 [OHLCV Data Refresh] - OHLCV process
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072342_0003 [SEC Data Synchronization] - SEC process
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072342_0001 [DMI Calculation] - Process 1
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072342_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072342_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072342_0001 [DMI Calculation] - Test process
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072342_0001 [DMI Calculation] - Test process
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: dmi_calculation_202506072342_0001
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072342_0001 [DMI Calculation] - DMI 1
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072342_0002 [DMI Calculation] - DMI 2
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072342_0003 [OHLCV Data Refresh] - OHLCV 1
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072342_0004 [SEC Data Synchronization] - SEC 1
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072342_0001 [DMI Calculation] - Process 1
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072342_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072342_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Cleaned up 2 completed processes
2025-06-07 23:42:06 [Test worker] INFO  c.i.api.controller.ProcessController - Received request to abort all active processes
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for 0 active processes
2025-06-07 23:42:06 [Test worker] INFO  c.investment.process.ProcessManager - Abort all processes completed: 0/0 processes abort requested
2025-06-07 23:42:06 [Test worker] INFO  c.i.api.controller.ProcessController - Abort all processes completed: No active processes found to abort
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Test DMI calculation
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Test process
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0002 [DMI Calculation] - Test process
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0003 [DMI Calculation] - Test process
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Process 1
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072349_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072349_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Test process
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: dmi_calculation_202506072349_0001
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Process 1
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072349_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072349_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for 2 active processes
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: ohlcv_refresh_202506072349_0002
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Abort requested for process: ohlcv_refresh_202506072349_0002 [OHLCV Data Refresh]
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: dmi_calculation_202506072349_0001
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Abort requested for process: dmi_calculation_202506072349_0001 [DMI Calculation]
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Abort all processes completed: 2/2 processes abort requested
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - DMI process
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072349_0002 [OHLCV Data Refresh] - OHLCV process
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072349_0003 [SEC Data Synchronization] - SEC process
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Process 1
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072349_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072349_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Process 1
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072349_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072349_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Cleaned up 2 completed processes
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - DMI 1
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0002 [DMI Calculation] - DMI 2
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072349_0003 [OHLCV Data Refresh] - OHLCV 1
2025-06-07 23:49:11 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072349_0004 [SEC Data Synchronization] - SEC 1
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Process 1
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072349_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072349_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:49:47 [Test worker] INFO  c.i.api.controller.ProcessController - Received request to abort all active processes
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for 2 active processes
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: ohlcv_refresh_202506072349_0002
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Abort requested for process: ohlcv_refresh_202506072349_0002 [OHLCV Data Refresh]
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: dmi_calculation_202506072349_0001
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Abort requested for process: dmi_calculation_202506072349_0001 [DMI Calculation]
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Abort all processes completed: 2/2 processes abort requested
2025-06-07 23:49:47 [Test worker] INFO  c.i.api.controller.ProcessController - Abort all processes completed: Successfully requested abort for all 2 active processes
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Process 1
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072349_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Process 1
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072349_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072349_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - DMI process
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072349_0002 [OHLCV Data Refresh] - OHLCV process
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072349_0003 [SEC Data Synchronization] - SEC process
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Process 1
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072349_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072349_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Test process
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Test process
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for process: dmi_calculation_202506072349_0001
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - DMI 1
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0002 [DMI Calculation] - DMI 2
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072349_0003 [OHLCV Data Refresh] - OHLCV 1
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072349_0004 [SEC Data Synchronization] - SEC 1
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: dmi_calculation_202506072349_0001 [DMI Calculation] - Process 1
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: ohlcv_refresh_202506072349_0002 [OHLCV Data Refresh] - Process 2
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Registered new process: sec_synchronization_202506072349_0003 [SEC Data Synchronization] - Process 3
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Cleaned up 2 completed processes
2025-06-07 23:49:47 [Test worker] INFO  c.i.api.controller.ProcessController - Received request to abort all active processes
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Requesting abort for 0 active processes
2025-06-07 23:49:47 [Test worker] INFO  c.investment.process.ProcessManager - Abort all processes completed: 0/0 processes abort requested
2025-06-07 23:49:47 [Test worker] INFO  c.i.api.controller.ProcessController - Abort all processes completed: No active processes found to abort
