package com.investment.api.model;

import com.investment.model.WatchListItem;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;

/**
 * Request model for creating a new watch list item.
 */
@Schema(description = "Request to create a new watch list item")
public class CreateWatchListRequest {

    @NotNull(message = "Display index is required")
    @Min(value = 0, message = "Display index must be non-negative")
    @Schema(description = "Display index for custom ordering", example = "1", required = true)
    private Integer displayIndex;

    @NotBlank(message = "Symbol is required")
    @Size(max = 20, message = "Symbol cannot exceed 20 characters")
    @Schema(description = "Financial instrument symbol", example = "AAPL", required = true)
    private String symbol;

    @NotNull(message = "Start date is required")
    @Schema(description = "Date when symbol was added to watch list", example = "2024-01-15", required = true)
    private LocalDate startDate;

    @Size(max = 128, message = "Remarks cannot exceed 128 characters")
    @Schema(description = "Optional notes/comments about the symbol", example = "Strong growth potential")
    private String remarks;

    /**
     * Default constructor.
     */
    public CreateWatchListRequest() {
    }

    /**
     * Constructor with required fields.
     */
    public CreateWatchListRequest(Integer displayIndex, String symbol, LocalDate startDate) {
        this.displayIndex = displayIndex;
        this.symbol = symbol;
        this.startDate = startDate;
    }

    /**
     * Constructor with all fields.
     */
    public CreateWatchListRequest(Integer displayIndex, String symbol, LocalDate startDate, String remarks) {
        this.displayIndex = displayIndex;
        this.symbol = symbol;
        this.startDate = startDate;
        this.remarks = remarks;
    }

    /**
     * Convert to domain model.
     */
    public WatchListItem toWatchListItem() {
        WatchListItem item = new WatchListItem(displayIndex, symbol, startDate, remarks);
        item.validate();
        return item;
    }

    // Getters and Setters

    public Integer getDisplayIndex() {
        return displayIndex;
    }

    public void setDisplayIndex(Integer displayIndex) {
        this.displayIndex = displayIndex;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Override
    public String toString() {
        return "CreateWatchListRequest{" +
                "displayIndex=" + displayIndex +
                ", symbol='" + symbol + '\'' +
                ", startDate=" + startDate +
                ", remarks='" + remarks + '\'' +
                '}';
    }
}
