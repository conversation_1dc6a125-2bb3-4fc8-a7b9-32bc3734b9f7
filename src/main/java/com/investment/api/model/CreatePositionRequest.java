package com.investment.api.model;

import com.investment.model.Position;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

/**
 * Request model for creating a new position.
 */
@Schema(description = "Request parameters for creating a new portfolio position")
public class CreatePositionRequest {
    
    @Schema(description = "Financial instrument symbol", example = "AAPL", required = true)
    @NotBlank(message = "Symbol is required")
    @Size(max = 20, message = "Symbol must not exceed 20 characters")
    private String symbol;
    
    @Schema(description = "Quantity of shares/units (positive for long, negative for short)", 
            example = "100.0", required = true)
    @NotNull(message = "Position quantity is required")
    @DecimalMin(value = "-999999999.999999", message = "Position quantity is too small")
    @DecimalMax(value = "999999999.999999", message = "Position quantity is too large")
    private BigDecimal position;
    
    @Schema(description = "Position direction", example = "BUY", required = true)
    @NotNull(message = "Side is required")
    private Position.Side side;
    
    @Schema(description = "Price at which the position was opened", 
            example = "150.25", required = true)
    @NotNull(message = "Trade price is required")
    @DecimalMin(value = "0.000001", message = "Trade price must be positive")
    @DecimalMax(value = "999999999.999999", message = "Trade price is too large")
    private BigDecimal tradePrice;
    
    @Schema(description = "Total portfolio net value when position was opened", 
            example = "100000.00")
    @DecimalMin(value = "0.0", message = "Initial portfolio net value must be non-negative")
    @DecimalMax(value = "999999999.999999", message = "Initial portfolio net value is too large")
    private BigDecimal initPortfolioNetValue;
    
    @Schema(description = "Risk unit size for position sizing calculations", 
            example = "1000.00")
    @DecimalMin(value = "0.0", message = "Risk unit must be non-negative")
    @DecimalMax(value = "999999999.999999", message = "Risk unit is too large")
    private BigDecimal riskUnit;
    
    @Schema(description = "Stop loss percentage (e.g., 0.02 for 2%)", 
            example = "0.02")
    @DecimalMin(value = "0.0", message = "Stop percent must be non-negative")
    @DecimalMax(value = "1.0", message = "Stop percent must not exceed 100%")
    private BigDecimal stopPercent;
    
    @Schema(description = "Bollinger Band Middle Band adjustment percentage", 
            example = "0.01")
    @DecimalMin(value = "0.0", message = "BBMB adjustment percent must be non-negative")
    @DecimalMax(value = "1.0", message = "BBMB adjustment percent must not exceed 100%")
    private BigDecimal bbmbAdjPercent;
    
    // Default constructor
    public CreatePositionRequest() {}
    
    // Constructor with required fields
    public CreatePositionRequest(String symbol, BigDecimal position, Position.Side side, BigDecimal tradePrice) {
        this.symbol = symbol;
        this.position = position;
        this.side = side;
        this.tradePrice = tradePrice;
    }
    
    // Getters and Setters
    public String getSymbol() { return symbol; }
    public void setSymbol(String symbol) { this.symbol = symbol; }
    
    public BigDecimal getPosition() { return position; }
    public void setPosition(BigDecimal position) { this.position = position; }
    
    public Position.Side getSide() { return side; }
    public void setSide(Position.Side side) { this.side = side; }
    
    public BigDecimal getTradePrice() { return tradePrice; }
    public void setTradePrice(BigDecimal tradePrice) { this.tradePrice = tradePrice; }
    
    public BigDecimal getInitPortfolioNetValue() { return initPortfolioNetValue; }
    public void setInitPortfolioNetValue(BigDecimal initPortfolioNetValue) { this.initPortfolioNetValue = initPortfolioNetValue; }
    
    public BigDecimal getRiskUnit() { return riskUnit; }
    public void setRiskUnit(BigDecimal riskUnit) { this.riskUnit = riskUnit; }
    
    public BigDecimal getStopPercent() { return stopPercent; }
    public void setStopPercent(BigDecimal stopPercent) { this.stopPercent = stopPercent; }
    
    public BigDecimal getBbmbAdjPercent() { return bbmbAdjPercent; }
    public void setBbmbAdjPercent(BigDecimal bbmbAdjPercent) { this.bbmbAdjPercent = bbmbAdjPercent; }
    
    /**
     * Convert this request to a Position entity.
     */
    public Position toPosition() {
        Position position = new Position(symbol, this.position, side, tradePrice);
        position.setInitPortfolioNetValue(initPortfolioNetValue);
        position.setRiskUnit(riskUnit);
        position.setStopPercent(stopPercent);
        position.setBbmbAdjPercent(bbmbAdjPercent);
        
        // Calculate trade value
        position.setTradeValue(this.position.multiply(tradePrice));
        
        return position;
    }
    
    @Override
    public String toString() {
        return String.format("CreatePositionRequest{symbol='%s', position=%s, side=%s, tradePrice=%s, initPortfolioNetValue=%s, riskUnit=%s, stopPercent=%s, bbmbAdjPercent=%s}",
                symbol, position, side, tradePrice, initPortfolioNetValue, riskUnit, stopPercent, bbmbAdjPercent);
    }
}
