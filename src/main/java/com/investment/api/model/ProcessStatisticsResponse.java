package com.investment.api.model;

import com.investment.process.ProcessManager;
import com.investment.process.ProcessStatus;
import com.investment.process.ProcessType;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * API response model for process statistics.
 */
@Schema(description = "Statistics about processes in the system")
public class ProcessStatisticsResponse {
    
    @Schema(description = "Total number of processes (including completed ones)", example = "25")
    private final int totalProcesses;
    
    @Schema(description = "Number of currently active processes", example = "3")
    private final int activeProcesses;
    
    @Schema(description = "Count of processes by status")
    private final Map<ProcessStatus, Long> statusCounts;
    
    @Schema(description = "Count of processes by type")
    private final Map<ProcessType, Long> typeCounts;
    
    @Schema(description = "When these statistics were generated")
    private final LocalDateTime timestamp;
    
    /**
     * Create response from ProcessManager.ProcessStatistics.
     */
    public ProcessStatisticsResponse(ProcessManager.ProcessStatistics statistics) {
        this.totalProcesses = statistics.getTotalProcesses();
        this.activeProcesses = statistics.getActiveProcesses();
        this.statusCounts = statistics.getStatusCounts();
        this.typeCounts = statistics.getTypeCounts();
        this.timestamp = LocalDateTime.now();
    }
    
    // Getters
    public int getTotalProcesses() { return totalProcesses; }
    public int getActiveProcesses() { return activeProcesses; }
    public Map<ProcessStatus, Long> getStatusCounts() { return statusCounts; }
    public Map<ProcessType, Long> getTypeCounts() { return typeCounts; }
    public LocalDateTime getTimestamp() { return timestamp; }
}
